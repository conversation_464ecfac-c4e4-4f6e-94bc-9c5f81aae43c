// ignore_for_file: library_private_types_in_public_api

import 'dart:io';

import 'package:flick_video_player/flick_video_player.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/core/utils/loading_animation_widget.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/portrait_controls.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:zoom_pinch_overlay/zoom_pinch_overlay.dart';

class FlickMultiPlayer extends StatefulWidget {
  const FlickMultiPlayer({super.key, this.url, this.file, this.image, required this.flickMultiManager});

  final String? url;
  final File? file;
  final String? image;
  final FlickMultiManager flickMultiManager;

  @override
  _FlickMultiPlayerState createState() => _FlickMultiPlayerState();
}

class _FlickMultiPlayerState extends State<FlickMultiPlayer> {
  late FlickManager flickManager;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeVideoPlayer();
  }

  void _initializeVideoPlayer() async {
    try {
      VideoPlayerController controller;

      if (widget.file != null) {
        controller = VideoPlayerController.file(widget.file!);
      } else {
        // For iOS, ensure proper URL handling
        final uri = Uri.parse(widget.url!);
        if (Platform.isIOS) {
          // Use networkUrl for iOS with proper configuration
          controller = VideoPlayerController.networkUrl(
            uri,
            videoPlayerOptions: VideoPlayerOptions(
              mixWithOthers: true,
              allowBackgroundPlayback: false,
            ),
          );
        } else {
          controller = VideoPlayerController.networkUrl(uri);
        }
      }

      await controller.setLooping(true);

      flickManager = FlickManager(
        videoPlayerController: controller,
        autoPlay: false, // Keep false for manual control
      );

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
        Logger.lOG('Video player initialized successfully for ${Platform.isIOS ? "iOS" : "Android"}');
      }
    } catch (e) {
      Logger.lOG('Error initializing video player: $e');
      // Fallback initialization
      try {
        flickManager = FlickManager(
          videoPlayerController: widget.file != null
              ? VideoPlayerController.file(widget.file!)
              : VideoPlayerController.networkUrl(Uri.parse(widget.url!))
            ..setLooping(true),
          autoPlay: false,
        );

        if (mounted) {
          setState(() {
            _isInitialized = true;
          });
          Logger.lOG('Video player fallback initialization successful');
        }
      } catch (fallbackError) {
        Logger.lOG('Fallback video player initialization failed: $fallbackError');
      }
    }
  }

  @override
  void dispose() {
    //* Removes the video player controller from the manager when the widget is disposed.
    if (_isInitialized) {
      widget.flickMultiManager.remove(flickManager);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      // Show loading state with thumbnail
      return Container(
        color: Colors.black,
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (widget.image != null)
              CustomImageView(
                imagePath: widget.image!,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
              ),
            LoadingAnimationWidget()
          ],
        ),
      );
    }

    return VisibilityDetector(
      key: ObjectKey(flickManager),
      onVisibilityChanged: (visiblityInfo) {
        //* For iOS, use a lower threshold and add delay for better detection
        final threshold = Platform.isIOS ? 0.3 : 0.5;
        final isVisible = visiblityInfo.visibleFraction > threshold;

        Logger.lOG(
            'Video visibility changed: ${visiblityInfo.visibleFraction}, threshold: $threshold, isVisible: $isVisible, platform: ${Platform.isIOS ? "iOS" : "Android"}');

        if (isVisible) {
          // Add small delay for iOS to ensure proper initialization
          if (Platform.isIOS) {
            Future.delayed(const Duration(milliseconds: 100), () {
              if (mounted && _isInitialized) {
                Logger.lOG('Playing video on iOS with delay');
                widget.flickMultiManager.play(flickManager);
              }
            });
          } else {
            Logger.lOG('Playing video on Android immediately');
            widget.flickMultiManager.play(flickManager);
          }
        } else {
          Logger.lOG('Pausing video due to visibility');
          widget.flickMultiManager.pause();
        }
      },
      child: ZoomOverlay(
        // modalBarrierColor: Colors.black.withOpacity(0.8),
        // minScale: 1.0,
        // maxScale: 3.0,
        twoTouchOnly: true, // only zoom with 2 fingers
        // animationDuration: Duration(milliseconds: 300),
        child: FlickVideoPlayer(
          flickManager: flickManager,
          flickVideoWithControls: FlickVideoWithControls(
            playerLoadingFallback: Positioned.fill(
              child: Stack(
                alignment: Alignment.center,
                children: <Widget>[
                  Positioned.fill(
                    child: CustomImageView(
                      imagePath: widget.image!,
                      fit: BoxFit.cover,
                    ),
                  ),
                  LoadingAnimationWidget()
                  // const CircularProgressIndicator(
                  //   color: Colors.white,
                  // ),
                ],
              ),
            ),
            controls: FeedPlayerPortraitControls(
              flickMultiManager: widget.flickMultiManager,
              flickManager: flickManager,
            ),
          ),
          flickVideoWithControlsFullscreen: FlickVideoWithControls(
            backgroundColor: Theme.of(context).customColors.white,
            playerLoadingFallback: Center(
                child: CustomImageView(
              imagePath: widget.image!,
              fit: BoxFit.fitWidth,
            )),
            iconThemeData: IconThemeData(size: 40.sp, color: Theme.of(context).customColors.white),
            textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Theme.of(context).customColors.white),
          ),
        ),
      ),
    );
  }
}
